/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-title {
    color: #667eea;
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
}

/* 页面容器 */
.page {
    display: none;
    margin-top: 70px;
    padding: 30px 0;
}

.page.active {
    display: block;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h2 {
    color: #667eea;
    font-size: 2rem;
    font-weight: 600;
}

.date-display {
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
}

/* 卡片样式 */
.task-section,
.reflection-section,
.improvement-section,
.chart-section,
.expense-input-section,
.expense-stats-section,
.expense-list-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.task-section h3,
.reflection-section h3,
.improvement-section h3,
.chart-section h3,
.expense-input-section h3,
.expense-stats-section h3,
.expense-list-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

/* 输入组件样式 */
.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.input-group input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 进度条样式 */
.progress-area {
    margin-bottom: 30px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: #e1e5e9;
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 6px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-weight: 600;
    color: #667eea;
    min-width: 40px;
}

.task-stats {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 0.9rem;
}

/* 任务列表样式 */
.task-list {
    list-style: none;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.task-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.task-item.completed {
    opacity: 0.7;
    text-decoration: line-through;
}

.task-checkbox {
    margin-right: 15px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.task-text {
    flex: 1;
    font-size: 1rem;
}

.task-delete {
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.task-delete:hover {
    background: #ff5252;
    transform: scale(1.05);
}

/* 文本域样式 */
textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 反思显示区域 */
.reflection-display {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    border-left: 4px solid #667eea;
    min-height: 50px;
}

.reflection-entry {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.reflection-entry:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.reflection-date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.reflection-content {
    color: #333;
    line-height: 1.6;
}

/* 改进建议列表 */
.improvement-list {
    list-style: none;
}

.improvement-item {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.improvement-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.improvement-text {
    flex: 1;
    color: #856404;
}

.improvement-delete {
    background: #ffc107;
    color: #856404;
    border: none;
    border-radius: 5px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.improvement-delete:hover {
    background: #ffb300;
    transform: scale(1.05);
}

/* 图表容器 */
.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    height: 400px;
    position: relative;
}

/* 支出记录页面样式 */
.expense-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row input,
.form-row select {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-row input:focus,
.form-row select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h4 {
    font-size: 1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
}

/* 支出筛选 */
.expense-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.expense-filters select {
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.expense-filters select:focus {
    outline: none;
    border-color: #667eea;
}

/* 支出列表 */
.expense-list {
    max-height: 400px;
    overflow-y: auto;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.expense-item:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.expense-info {
    flex: 1;
}

.expense-description {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.expense-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #666;
}

.expense-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: #e74c3c;
}

.expense-delete {
    background: #ff6b6b;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-left: 15px;
    transition: all 0.3s ease;
}

.expense-delete:hover {
    background: #ff5252;
    transform: scale(1.05);
}

/* 图表标签 */
.chart-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.chart-tab {
    padding: 10px 20px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.chart-tab.active,
.chart-tab:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
    }

    .nav-menu {
        margin-top: 15px;
        gap: 15px;
    }

    .page {
        margin-top: 120px;
    }

    .page-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .input-group,
    .form-row {
        flex-direction: column;
    }

    .expense-filters {
        flex-direction: column;
    }

    .expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .expense-meta {
        flex-direction: column;
        gap: 5px;
    }

    .chart-tabs {
        flex-direction: column;
    }

    .container {
        padding: 0 15px;
    }
}
