<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <h1 class="nav-title">个人管理系统</h1>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#daily-plan" class="nav-link active" data-page="daily-plan">每日计划</a>
                </li>
                <li class="nav-item">
                    <a href="#expense-record" class="nav-link" data-page="expense-record">支出记录</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 每日计划页面 -->
    <div id="daily-plan" class="page active">
        <div class="container">
            <div class="page-header">
                <h2>每日计划</h2>
                <div class="date-display">
                    <span id="current-date"></span>
                </div>
            </div>

            <!-- 任务管理区域 -->
            <div class="task-section">
                <div class="task-input-area">
                    <h3>添加今日任务</h3>
                    <div class="input-group">
                        <input type="text" id="task-input" placeholder="输入任务内容..." maxlength="100">
                        <button id="add-task-btn" class="btn btn-primary">添加任务</button>
                    </div>
                </div>

                <div class="progress-area">
                    <h3>今日进度</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <span id="progress-text" class="progress-text">0%</span>
                    </div>
                    <div class="task-stats">
                        <span>已完成: <span id="completed-count">0</span></span>
                        <span>总任务: <span id="total-count">0</span></span>
                    </div>
                </div>

                <div class="task-list-area">
                    <h3>任务列表</h3>
                    <ul id="task-list" class="task-list"></ul>
                </div>
            </div>

            <!-- 感想记录区域 -->
            <div class="reflection-section">
                <h3>今日感想</h3>
                <div class="reflection-input">
                    <textarea id="reflection-text" placeholder="记录今天的感想、收获或思考..." rows="4"></textarea>
                    <button id="save-reflection-btn" class="btn btn-secondary">保存感想</button>
                </div>
                <div id="reflection-display" class="reflection-display"></div>
            </div>

            <!-- 改进建议区域 -->
            <div class="improvement-section">
                <h3>下次改进</h3>
                <div class="improvement-input">
                    <input type="text" id="improvement-input" placeholder="记录下次需要改进的地方..." maxlength="200">
                    <button id="add-improvement-btn" class="btn btn-secondary">添加改进</button>
                </div>
                <ul id="improvement-list" class="improvement-list"></ul>
            </div>

            <!-- 完成率图表 -->
            <div class="chart-section">
                <h3>完成率趋势</h3>
                <div class="chart-container">
                    <canvas id="completion-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 支出记录页面 -->
    <div id="expense-record" class="page">
        <div class="container">
            <div class="page-header">
                <h2>支出记录</h2>
                <div class="date-display">
                    <span id="expense-date"></span>
                </div>
            </div>

            <!-- 支出添加区域 -->
            <div class="expense-input-section">
                <h3>添加支出</h3>
                <div class="expense-form">
                    <div class="form-row">
                        <input type="number" id="expense-amount" placeholder="金额" step="0.01" min="0">
                        <select id="expense-category">
                            <option value="餐饮">餐饮</option>
                            <option value="交通">交通</option>
                            <option value="购物">购物</option>
                            <option value="娱乐">娱乐</option>
                            <option value="学习">学习</option>
                            <option value="医疗">医疗</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="form-row">
                        <input type="text" id="expense-description" placeholder="支出描述" maxlength="100">
                        <input type="date" id="expense-date-input">
                    </div>
                    <button id="add-expense-btn" class="btn btn-primary">添加支出</button>
                </div>
            </div>

            <!-- 支出统计 -->
            <div class="expense-stats-section">
                <h3>支出统计</h3>
                <div class="stats-cards">
                    <div class="stat-card">
                        <h4>今日支出</h4>
                        <span id="today-expense" class="stat-value">¥0.00</span>
                    </div>
                    <div class="stat-card">
                        <h4>本周支出</h4>
                        <span id="week-expense" class="stat-value">¥0.00</span>
                    </div>
                    <div class="stat-card">
                        <h4>本月支出</h4>
                        <span id="month-expense" class="stat-value">¥0.00</span>
                    </div>
                </div>
            </div>

            <!-- 支出列表 -->
            <div class="expense-list-section">
                <h3>支出记录</h3>
                <div class="expense-filters">
                    <select id="filter-category">
                        <option value="">所有分类</option>
                        <option value="餐饮">餐饮</option>
                        <option value="交通">交通</option>
                        <option value="购物">购物</option>
                        <option value="娱乐">娱乐</option>
                        <option value="学习">学习</option>
                        <option value="医疗">医疗</option>
                        <option value="其他">其他</option>
                    </select>
                    <select id="filter-period">
                        <option value="all">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                    </select>
                </div>
                <div id="expense-list" class="expense-list"></div>
            </div>

            <!-- 支出图表 -->
            <div class="chart-section">
                <h3>支出分析</h3>
                <div class="chart-tabs">
                    <button class="chart-tab active" data-chart="category">分类统计</button>
                    <button class="chart-tab" data-chart="trend">支出趋势</button>
                </div>
                <div class="chart-container">
                    <canvas id="expense-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
