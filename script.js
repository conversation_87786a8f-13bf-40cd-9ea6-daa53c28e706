// 全局变量
let currentPage = 'daily-plan';
let completionChart = null;
let expenseChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    setupNavigation();
    setupDailyPlan();
    setupExpenseRecord();
    updateDateDisplays();
    loadData();
}

// 设置导航
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const pages = document.querySelectorAll('.page');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetPage = link.getAttribute('data-page');
            
            // 更新导航状态
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            
            // 切换页面
            pages.forEach(page => page.classList.remove('active'));
            document.getElementById(targetPage).classList.add('active');
            
            currentPage = targetPage;
            
            // 根据页面更新图表
            if (targetPage === 'daily-plan') {
                updateCompletionChart();
            } else if (targetPage === 'expense-record') {
                updateExpenseChart();
                updateExpenseStats();
            }
        });
    });
}

// 设置每日计划功能
function setupDailyPlan() {
    // 添加任务
    const taskInput = document.getElementById('task-input');
    const addTaskBtn = document.getElementById('add-task-btn');
    
    addTaskBtn.addEventListener('click', addTask);
    taskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') addTask();
    });
    
    // 保存感想
    const saveReflectionBtn = document.getElementById('save-reflection-btn');
    saveReflectionBtn.addEventListener('click', saveReflection);
    
    // 添加改进建议
    const improvementInput = document.getElementById('improvement-input');
    const addImprovementBtn = document.getElementById('add-improvement-btn');
    
    addImprovementBtn.addEventListener('click', addImprovement);
    improvementInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') addImprovement();
    });
    
    // 加载今日数据
    loadTodayData();
}

// 设置支出记录功能
function setupExpenseRecord() {
    // 添加支出
    const addExpenseBtn = document.getElementById('add-expense-btn');
    addExpenseBtn.addEventListener('click', addExpense);
    
    // 设置默认日期
    const expenseDateInput = document.getElementById('expense-date-input');
    expenseDateInput.value = new Date().toISOString().split('T')[0];
    
    // 筛选功能
    const filterCategory = document.getElementById('filter-category');
    const filterPeriod = document.getElementById('filter-period');
    
    filterCategory.addEventListener('change', filterExpenses);
    filterPeriod.addEventListener('change', filterExpenses);
    
    // 图表切换
    const chartTabs = document.querySelectorAll('.chart-tab');
    chartTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            chartTabs.forEach(t => t.classList.remove('active'));
            e.target.classList.add('active');
            updateExpenseChart(e.target.getAttribute('data-chart'));
        });
    });
}

// 更新日期显示
function updateDateDisplays() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });
    
    document.getElementById('current-date').textContent = dateStr;
    document.getElementById('expense-date').textContent = dateStr;
}

// 添加任务
function addTask() {
    const taskInput = document.getElementById('task-input');
    const taskText = taskInput.value.trim();
    
    if (!taskText) return;
    
    const task = {
        id: Date.now(),
        text: taskText,
        completed: false,
        date: new Date().toISOString().split('T')[0]
    };
    
    // 保存到本地存储
    const tasks = getTasks();
    tasks.push(task);
    localStorage.setItem('tasks', JSON.stringify(tasks));
    
    // 更新界面
    renderTasks();
    updateProgress();
    taskInput.value = '';
    
    // 更新图表
    updateCompletionChart();
}

// 获取任务列表
function getTasks() {
    return JSON.parse(localStorage.getItem('tasks') || '[]');
}

// 获取今日任务
function getTodayTasks() {
    const today = new Date().toISOString().split('T')[0];
    return getTasks().filter(task => task.date === today);
}

// 渲染任务列表
function renderTasks() {
    const taskList = document.getElementById('task-list');
    const todayTasks = getTodayTasks();
    
    taskList.innerHTML = '';
    
    todayTasks.forEach(task => {
        const li = document.createElement('li');
        li.className = `task-item ${task.completed ? 'completed' : ''}`;
        li.innerHTML = `
            <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''} 
                   onchange="toggleTask(${task.id})">
            <span class="task-text">${task.text}</span>
            <button class="task-delete" onclick="deleteTask(${task.id})">删除</button>
        `;
        taskList.appendChild(li);
    });
}

// 切换任务状态
function toggleTask(taskId) {
    const tasks = getTasks();
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.completed = !task.completed;
        localStorage.setItem('tasks', JSON.stringify(tasks));
        renderTasks();
        updateProgress();
        updateCompletionChart();
    }
}

// 删除任务
function deleteTask(taskId) {
    const tasks = getTasks();
    const filteredTasks = tasks.filter(t => t.id !== taskId);
    localStorage.setItem('tasks', JSON.stringify(filteredTasks));
    renderTasks();
    updateProgress();
    updateCompletionChart();
}

// 更新进度
function updateProgress() {
    const todayTasks = getTodayTasks();
    const completedTasks = todayTasks.filter(task => task.completed);
    const progress = todayTasks.length > 0 ? (completedTasks.length / todayTasks.length) * 100 : 0;
    
    document.getElementById('progress-fill').style.width = `${progress}%`;
    document.getElementById('progress-text').textContent = `${Math.round(progress)}%`;
    document.getElementById('completed-count').textContent = completedTasks.length;
    document.getElementById('total-count').textContent = todayTasks.length;
}

// 保存感想
function saveReflection() {
    const reflectionText = document.getElementById('reflection-text');
    const text = reflectionText.value.trim();
    
    if (!text) return;
    
    const reflection = {
        id: Date.now(),
        text: text,
        date: new Date().toISOString().split('T')[0],
        timestamp: new Date().toLocaleString('zh-CN')
    };
    
    const reflections = getReflections();
    reflections.push(reflection);
    localStorage.setItem('reflections', JSON.stringify(reflections));
    
    renderReflections();
    reflectionText.value = '';
}

// 获取感想列表
function getReflections() {
    return JSON.parse(localStorage.getItem('reflections') || '[]');
}

// 渲染感想
function renderReflections() {
    const reflectionDisplay = document.getElementById('reflection-display');
    const reflections = getReflections().slice(-5); // 只显示最近5条
    
    if (reflections.length === 0) {
        reflectionDisplay.innerHTML = '<p style="color: #666; text-align: center;">暂无感想记录</p>';
        return;
    }
    
    reflectionDisplay.innerHTML = reflections.map(reflection => `
        <div class="reflection-entry">
            <div class="reflection-date">${reflection.timestamp}</div>
            <div class="reflection-content">${reflection.text}</div>
        </div>
    `).join('');
}

// 添加改进建议
function addImprovement() {
    const improvementInput = document.getElementById('improvement-input');
    const text = improvementInput.value.trim();
    
    if (!text) return;
    
    const improvement = {
        id: Date.now(),
        text: text,
        date: new Date().toISOString().split('T')[0]
    };
    
    const improvements = getImprovements();
    improvements.push(improvement);
    localStorage.setItem('improvements', JSON.stringify(improvements));
    
    renderImprovements();
    improvementInput.value = '';
}

// 获取改进建议
function getImprovements() {
    return JSON.parse(localStorage.getItem('improvements') || '[]');
}

// 渲染改进建议
function renderImprovements() {
    const improvementList = document.getElementById('improvement-list');
    const improvements = getImprovements();
    
    improvementList.innerHTML = '';
    
    improvements.forEach(improvement => {
        const li = document.createElement('li');
        li.className = 'improvement-item';
        li.innerHTML = `
            <span class="improvement-text">${improvement.text}</span>
            <button class="improvement-delete" onclick="deleteImprovement(${improvement.id})">删除</button>
        `;
        improvementList.appendChild(li);
    });
}

// 删除改进建议
function deleteImprovement(improvementId) {
    const improvements = getImprovements();
    const filteredImprovements = improvements.filter(i => i.id !== improvementId);
    localStorage.setItem('improvements', JSON.stringify(filteredImprovements));
    renderImprovements();
}

// 加载今日数据
function loadTodayData() {
    renderTasks();
    updateProgress();
    renderReflections();
    renderImprovements();
}

// 加载所有数据
function loadData() {
    loadTodayData();
    if (currentPage === 'expense-record') {
        renderExpenses();
        updateExpenseStats();
    }
}
